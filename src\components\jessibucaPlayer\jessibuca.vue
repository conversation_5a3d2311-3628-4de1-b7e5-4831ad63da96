<template>
  <div ref="container" class="jessibuca-player" @dblclick="fullscreenSwich">
    <div class="aspect-ratio-box" />
    <div v-if="!playing && !loaded" class="play-overlay" @click="playBtnClick">
      <i class="iconfont icon-play play-icon" />
    </div>
    <div class="buttons-box" :class="{ 'buttons-visible': showControls }">
      <div class="buttons-box-left">
        <i v-if="!playing" class="iconfont icon-play jessibuca-btn" @click="playBtnClick" />
        <i v-else class="iconfont icon-pause jessibuca-btn" @click="pause" />
      </div>
      <div class="buttons-box-right">
        <i class="iconfont icon-camera1196054easyiconnet jessibuca-btn" @click="screenshot" />
        <i class="iconfont icon-shuaxin11 jessibuca-btn" @click="playBtnClick" />
        <i v-if="!fullscreen" class="iconfont icon-weibiaoti10 jessibuca-btn" @click="fullscreenSwich" />
        <i v-else class="iconfont icon-weibiaoti11 jessibuca-btn" @click="fullscreenSwich" />
      </div>
    </div>
  </div>
</template>

<script>
const jessibucaPlayers = new Map()

export default {
  name: 'Jessibuca',
  props: {
    videoUrl: {
      type: String,
      default: '',
    },
    error: {
      type: Function,
      default: null,
    },
    hasAudio: {
      type: Boolean,
      default: true,
    },
    height: {
      type: String,
      default: '100%',
    },
    autoplay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      playing: false,
      fullscreen: false,
      loaded: false,
      showControls: false,
      controlsTimer: null,
      resizeObserver: null,
    }
  },
  computed: {
    playerId() {
      return this._uid
    },
    jessibuca() {
      return jessibucaPlayers.get(this.playerId)
    },
  },
  watch: {
    videoUrl: {
      handler(newUrl) {
        if (newUrl && (this.autoplay || this.loaded)) {
          this.play(newUrl)
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.initPlayer()
    this.setupControlsAutoHide()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    initPlayer() {
      this.setupResizeObserver()
      if (this.autoplay && this.videoUrl) {
        this.play(this.videoUrl)
      }
    },

    setupResizeObserver() {
      if (this.$refs.container) {
        this.resizeObserver = new ResizeObserver(() => {
          if (this.jessibuca) {
            this.jessibuca.resize()
          }
        })
        this.resizeObserver.observe(this.$refs.container)
      }
    },

    setupControlsAutoHide() {
      const container = this.$refs.container
      if (!container) return

      // 初始显示控制栏
      this.showControls = true

      container.addEventListener('mouseenter', this.showControlsHandler)
      container.addEventListener('mouseleave', this.hideControlsHandler)
      container.addEventListener('mousemove', this.showControlsHandler)
    },

    showControlsHandler() {
      this.showControls = true
      clearTimeout(this.controlsTimer)
      // 延长自动隐藏时间到5秒
      this.controlsTimer = setTimeout(() => {
        this.showControls = false
      }, 5000)
    },

    hideControlsHandler() {
      clearTimeout(this.controlsTimer)
      this.showControls = false
    },

    createPlayer() {
      if (this.jessibuca) {
        this.destroyPlayer()
      }

      const options = {
        container: this.$refs.container,
        decoder: 'jessibuca/decoder.js',
        hasAudio: this.hasAudio,
        isResize: true,
        videoBuffer: 0.1,
        loadingText: '视频加载中...',
        operateBtns: {
          fullscreen: false,
          screenshot: false,
          play: false,
          audio: false,
        },
        supportDblclickFullscreen: false,
        useMSE: true,
        useWebFullScreen: true,
      }

      const player = new window.Jessibuca(options)
      jessibucaPlayers.set(this.playerId, player)

      this.bindEvents(player)
      return player
    },

    bindEvents(player) {
      player.on('play', () => {
        this.playing = true
        this.loaded = true
        // 播放开始时显示控制栏
        this.showControlsHandler()
      })

      player.on('pause', () => {
        this.playing = false
      })

      player.on('fullscreen', (isFullscreen) => {
        this.fullscreen = isFullscreen
      })

      player.on('error', (error) => {
        console.error('Jessibuca error:', error)
        if (this.error) {
          this.error(error)
        }
      })
    },

    async play(url) {
      if (!url) return

      const player = this.createPlayer()

      try {
        await player.play(url)
      } catch (error) {
        console.error('Play failed:', error)
      }
    },

    playBtnClick() {
      if (this.videoUrl) {
        this.play(this.videoUrl)
      }
    },

    pause() {
      if (this.jessibuca) {
        this.jessibuca.pause()
      }
    },

    screenshot() {
      if (this.jessibuca) {
        this.jessibuca.screenshot('截图', 'png', 0.8)
      }
    },

    destroy() {
      this.destroyPlayer()
      this.playing = false
      this.loaded = false
    },

    destroyPlayer() {
      if (this.jessibuca) {
        this.jessibuca.destroy()
        jessibucaPlayers.delete(this.playerId)
      }
    },

    fullscreenSwich() {
      if (this.jessibuca) {
        this.jessibuca.setFullscreen(!this.fullscreen)
      }
    },

    cleanup() {
      clearTimeout(this.controlsTimer)
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
      }
      this.destroyPlayer()
    },
  },
}
</script>

<style scoped>
.jessibuca-player {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.aspect-ratio-box {
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  position: relative;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 5;
}

.play-icon {
  font-size: 4rem;
  color: white;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.play-overlay:hover .play-icon {
  opacity: 1;
}

.buttons-box {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  opacity: 0.8;
  transition: opacity 0.3s;
  z-index: 10;
}

.buttons-visible {
  opacity: 1;
}

.buttons-box-left,
.buttons-box-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.jessibuca-btn {
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 14px;
  user-select: none;
}

.jessibuca-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@font-face {
  font-family: 'iconfont'; /* Project id 1291092 */
  src: url('iconfont.woff2?t=1637741914969') format('woff2');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-play:before {
  content: '\e603';
}

.icon-pause:before {
  content: '\e6c6';
}

.icon-stop:before {
  content: '\e6a8';
}

.icon-audio-high:before {
  content: '\e793';
}

.icon-audio-mute:before {
  content: '\e792';
}

.icon-shuaxin11:before {
  content: '\e720';
}

.icon-weibiaoti10:before {
  content: '\e78f';
}

.icon-weibiaoti11:before {
  content: '\e790';
}

.icon-camera1196054easyiconnet:before {
  content: '\e791';
}

</style>
